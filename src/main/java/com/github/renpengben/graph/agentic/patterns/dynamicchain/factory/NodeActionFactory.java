
package com.github.renpengben.graph.agentic.patterns.dynamicchain.factory;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.renpengben.graph.agentic.patterns.dynamicchain.model.LlmStepConfig;
import com.github.renpengben.graph.agentic.patterns.dynamicchain.model.NodeDefinition;
import com.github.renpengben.graph.agentic.patterns.dynamicchain.node.GenericGateNode;
import com.github.renpengben.graph.agentic.patterns.dynamicchain.node.GenericLlmStepNode;
import com.alibaba.cloud.ai.graph.action.NodeAction;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * 节点动作工厂类 (NodeAction Factory)。
 * 它的核心职责是根据节点的定义（特别是`nodeType`），创建出对应的具体`NodeAction`实例。
 * 这是实现动态工作流的关键，因为它将节点的“定义”和“执行”解耦开来。
 */
@Component
public class NodeActionFactory {

    private final ChatClient chatClient;
    private final ObjectMapper objectMapper;

    /**
     * 构造函数。
     * @param chatClient Spring AI的ChatClient，会注入到需要调用LLM的节点中。
     */
    public NodeActionFactory(ChatClient chatClient) {
        this.chatClient = chatClient;
        this.objectMapper = new ObjectMapper();
    }

    /**
     * 根据节点定义创建具体的NodeAction实例。
     * @param nodeDef 从数据库（或Mock）中读取的节点定义。
     * @return 一个实现了NodeAction接口的实例。
     */
    public NodeAction createNodeAction(NodeDefinition nodeDef) {
        // 使用Java 17的switch表达式，根据nodeType来决定创建哪种类型的节点
        return switch (nodeDef.getNodeType()) {
            case "LLM_STEP" -> {
                try {
                    // 如果是LLM节点，则解析其JSON配置到LlmStepConfig对象
                    LlmStepConfig config = objectMapper.readValue(nodeDef.getConfigJson(), LlmStepConfig.class);
                    // 创建并返回一个通用的LLM节点实例
                    yield new GenericLlmStepNode(chatClient, config);
                } catch (IOException e) {
                    throw new RuntimeException("解析LlmStepConfig JSON失败", e);
                }
            }
            case "CODE_GATE" ->
                // 如果是代码门节点，则创建一个通用的门节点实例
                    new GenericGateNode();
            default ->
                // 如果遇到未知的节点类型，则抛出异常
                    throw new IllegalArgumentException("未知的节点类型: " + nodeDef.getNodeType());
        };
    }
}
