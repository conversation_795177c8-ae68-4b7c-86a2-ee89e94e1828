
package com.github.renpengben.graph.agentic.patterns.dynamicchain.model;

import java.util.Map;

/**
 * Represents the definition of an edge connecting two nodes in a workflow graph.
 * An edge can be simple (a direct connection) or conditional (routing based on state).
 */
public class EdgeDefinition {

    /**
     * The name of the node where the edge originates.
     * Can be a special value like {@link com.alibaba.cloud.ai.graph.StateGraph#START}.
     */
    private String sourceNodeName;

    /**
     * The name of the node where the edge terminates.
     * Can be a special value like {@link com.alibaba.cloud.ai.graph.StateGraph#END}.
     * This is null for conditional edges, as targets are defined in targetMappings.
     */
    private String targetNodeName;

    /**
     * Flag indicating if this is a conditional edge.
     */
    private boolean isConditional;

    /**
     * For conditional edges, this is the key in the {@link com.alibaba.cloud.ai.graph.OverAllState}
     * whose value will be used to decide the next node.
     */
    private String conditionKey;

    /**
     * For conditional edges, this map defines the routing logic.
     * The key is the expected value from the state, and the value is the name of the target node.
     * (e.g., {"pass": "step2", "fail": "END"}).
     */
    private Map<String, String> targetMappings;

    /**
     * Constructor for a simple, unconditional edge.
     * @param sourceNodeName The name of the source node.
     * @param targetNodeName The name of the target node.
     */
    public EdgeDefinition(String sourceNodeName, String targetNodeName) {
        this.sourceNodeName = sourceNodeName;
        this.targetNodeName = targetNodeName;
        this.isConditional = false;
    }

    /**
     * Constructor for a conditional edge.
     * @param sourceNodeName The name of the source node (the node that produces the conditional value).
     * @param conditionKey The key in the state to check for routing.
     * @param targetMappings A map from condition values to target node names.
     */
    public EdgeDefinition(String sourceNodeName, String conditionKey, Map<String, String> targetMappings) {
        this.sourceNodeName = sourceNodeName;
        this.isConditional = true;
        this.conditionKey = conditionKey;
        this.targetMappings = targetMappings;
    }

    public String getSourceNodeName() {
        return sourceNodeName;
    }

    public String getTargetNodeName() {
        return targetNodeName;
    }

    public boolean isConditional() {
        return isConditional;
    }

    public String getConditionKey() {
        return conditionKey;
    }

    public Map<String, String> getTargetMappings() {
        return targetMappings;
    }
}
