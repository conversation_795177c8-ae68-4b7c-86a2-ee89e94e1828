
package com.github.renpengben.graph.agentic.patterns.dynamicchain.model;

/**
 * A Plain Old Java Object (POJO) for deserializing the JSON configuration
 * of a node with the type "LLM_STEP".
 */
public class LlmStepConfig {
    /**
     * The system prompt to be sent to the Language Model.
     */
    private String systemPrompt;
    /**
     * The key used to retrieve the input for the LLM from the {@link com.alibaba.cloud.ai.graph.OverAllState}.
     */
    private String inputKey;
    /**
     * The key used to store the output from the LLM into the {@link com.alibaba.cloud.ai.graph.OverAllState}.
     */
    private String outputKey;

    public String getSystemPrompt() {
        return systemPrompt;
    }

    public String getInputKey() {
        return inputKey;
    }

    public String getOutputKey() {
        return outputKey;
    }
}
