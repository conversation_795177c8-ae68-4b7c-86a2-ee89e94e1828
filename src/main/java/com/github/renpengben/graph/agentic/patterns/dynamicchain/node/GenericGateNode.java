
package com.github.renpengben.graph.agentic.patterns.dynamicchain.node;

import com.alibaba.cloud.ai.graph.OverAllState;
import com.alibaba.cloud.ai.graph.action.NodeAction;

import java.util.HashMap;
import java.util.Map;

/**
 * 一个通用的、程序化的“门”节点。
 * 这种节点不调用AI，而是执行一些代码逻辑来进行检查、判断或数据处理，
 * 通常用于决定工作流的走向（例如，通过/失败）。
 */
public class GenericGateNode implements NodeAction {

    /**
     * 执行节点的核心逻辑。
     * @param state 整个工作流的共享状态。
     * @return 一个Map，其中包含需要更新到状态中的键值对。
     */
    @Override
    public Map<String, Object> apply(OverAllState state) {
        // 在一个真实的应用程序中，这里会包含一些业务逻辑判断。
        // 例如，检查上一步的输出是否符合特定格式，或者查询数据库进行验证。
        // 在这个示例中，我们简单地让检查通过。
        Map<String, Object> checkResultMap = new HashMap<>();
        // 将检查结果"pass"放入状态中，供后续的条件边使用。
        checkResultMap.put("checked", "pass");
        return checkResultMap;
    }
}
