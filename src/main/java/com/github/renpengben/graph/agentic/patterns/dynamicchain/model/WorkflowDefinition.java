
package com.github.renpengben.graph.agentic.patterns.dynamicchain.model;

import java.util.List;

/**
 * Represents the definition of a workflow.
 * This class holds the high-level information about a dynamic graph,
 * including its name and the keys used to manage its state throughout the execution.
 */
public class WorkflowDefinition {

    /**
     * The unique name of the workflow.
     */
    private String name;

    /**
     * A list of keys that the workflow will use to store and retrieve data
     * from the shared {@link com.alibaba.cloud.ai.graph.OverAllState}.
     */
    private List<String> stateKeys;

    public WorkflowDefinition(String name, List<String> stateKeys) {
        this.name = name;
        this.stateKeys = stateKeys;
    }

    public String getName() {
        return name;
    }

    public List<String> getStateKeys() {
        return stateKeys;
    }
}
