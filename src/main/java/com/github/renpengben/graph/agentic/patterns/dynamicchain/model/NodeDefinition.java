
package com.github.renpengben.graph.agentic.patterns.dynamicchain.model;

/**
 * Represents the definition of a single node within a workflow graph.
 * Each node has a name, a type that determines its behavior, and a JSON configuration object.
 */
public class NodeDefinition {

    /**
     * The unique name of the node within the workflow (e.g., "step1", "gate").
     */
    private String nodeName;

    /**
     * The type of the node, which determines the action it will perform.
     * This is used by the {@link com.github.renpengben.graph.agentic.patterns.dynamicchain.factory.NodeActionFactory}.
     * (e.g., "LLM_STEP", "CODE_GATE").
     */
    private String nodeType;

    /**
     * A JSON string containing the specific configuration for this node.
     * The structure of this JSON depends on the nodeType.
     */
    private String configJson;

    public NodeDefinition(String nodeName, String nodeType, String configJson) {
        this.nodeName = nodeName;
        this.nodeType = nodeType;
        this.configJson = configJson;
    }

    public String getNodeName() {
        return nodeName;
    }

    public String getNodeType() {
        return nodeType;
    }

    public String getConfigJson() {
        return configJson;
    }
}
