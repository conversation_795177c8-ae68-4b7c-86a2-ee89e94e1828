
package com.github.renpengben.graph.agentic.patterns.dynamicchain.service;

import com.alibaba.cloud.ai.graph.OverAllState;
import com.alibaba.cloud.ai.graph.OverAllStateFactory;
import com.alibaba.cloud.ai.graph.StateGraph;
import com.alibaba.cloud.ai.graph.action.NodeAction;
import com.alibaba.cloud.ai.graph.exception.GraphStateException;
import com.alibaba.cloud.ai.graph.state.strategy.ReplaceStrategy;
import com.github.renpengben.graph.agentic.patterns.dynamicchain.factory.NodeActionFactory;
import com.github.renpengben.graph.agentic.patterns.dynamicchain.model.EdgeDefinition;
import com.github.renpengben.graph.agentic.patterns.dynamicchain.model.NodeDefinition;
import com.github.renpengben.graph.agentic.patterns.dynamicchain.model.WorkflowDefinition;
import com.github.renpengben.graph.agentic.patterns.dynamicchain.repository.WorkflowRepository;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import static com.alibaba.cloud.ai.graph.StateGraph.END;
import static com.alibaba.cloud.ai.graph.StateGraph.START;
import static com.alibaba.cloud.ai.graph.action.AsyncEdgeAction.edge_async;
import static com.alibaba.cloud.ai.graph.action.AsyncNodeAction.node_async;

/**
 * 动态图构建器服务。
 * 这是整个动态工作流机制的核心。它负责从数据源（在此为Mock仓库）获取工作流的完整定义
 * （包括节点、边、状态等），然后动态地将这些定义组装成一个可执行的 {@link StateGraph} 实例。
 */
@Service
public class DynamicGraphBuilder {

    private final WorkflowRepository workflowRepo;
    private final NodeActionFactory nodeActionFactory;
    // 使用ConcurrentHashMap作为缓存，避免每次请求都重新构建图，提高性能。
    private final Map<String, StateGraph> graphCache = new ConcurrentHashMap<>();

    public DynamicGraphBuilder(WorkflowRepository workflowRepo, NodeActionFactory nodeActionFactory) {
        this.workflowRepo = workflowRepo;
        this.nodeActionFactory = nodeActionFactory;
    }

    /**
     * 根据工作流名称构建（或从缓存获取）一个StateGraph。
     * @param workflowName 要构建的工作流的名称。
     * @return 一个完整的、可执行的StateGraph实例。
     */
    public StateGraph buildGraphByName(String workflowName) {
        // computeIfAbsent确保了构建过程对于同一个workflowName是线程安全的，且只执行一次。
        return graphCache.computeIfAbsent(workflowName, this::buildGraph);
    }

    /**
     * 实际的图构建逻辑。
     * @param workflowName 工作流名称。
     * @return 构建好的StateGraph。
     */
    private StateGraph buildGraph(String workflowName) {
        try {
            // 1. 从仓库获取工作流、节点和边的定义
            WorkflowDefinition workflowDef = workflowRepo.findWorkflowDefinitionByName(workflowName);
            if (workflowDef == null) {
                throw new IllegalArgumentException("未找到工作流: " + workflowName);
            }

            // 2. 动态创建状态工厂，用于初始化图的共享状态
            OverAllStateFactory factory = () -> {
                OverAllState s = new OverAllState();
                // 根据工作流定义，注册所有需要的状态键
                workflowDef.getStateKeys().forEach(key -> s.registerKeyAndStrategy(key, new ReplaceStrategy()));
                return s;
            };

            StateGraph graph = new StateGraph(workflowDef.getName(), factory.create());

            // 3. 动态添加所有节点
            List<NodeDefinition> nodeDefs = workflowRepo.findNodeDefinitionsByWorkflowName(workflowName);
            for (NodeDefinition nodeDef : nodeDefs) {
                // 使用NodeActionFactory创建节点的具体行为
                NodeAction action = nodeActionFactory.createNodeAction(nodeDef);
                graph.addNode(nodeDef.getNodeName(), node_async(action));
            }

            // 4. 动态添加所有边
            List<EdgeDefinition> edgeDefs = workflowRepo.findEdgeDefinitionsByWorkflowName(workflowName);
            for (EdgeDefinition edgeDef : edgeDefs) {
                if (edgeDef.isConditional()) {
                    // 添加条件边
                    graph.addConditionalEdges(edgeDef.getSourceNodeName(),
                            edge_async(t -> (String) t.value(edgeDef.getConditionKey()).orElse("fail")),
                            edgeDef.getTargetMappings());
                } else if (START.equals(edgeDef.getSourceNodeName())) {
                    // 添加入口边
                    graph.addEdge(START, edgeDef.getTargetNodeName());
                } else if (END.equals(edgeDef.getTargetNodeName())) {
                    // 添加出口边
                    graph.addEdge(edgeDef.getSourceNodeName(), END);
                } else {
                    // 添加普通边
                    graph.addEdge(edgeDef.getSourceNodeName(), edgeDef.getTargetNodeName());
                }
            }

            return graph;
        } catch (GraphStateException e) {
            throw new RuntimeException("构建图失败: " + workflowName, e);
        }
    }
}
