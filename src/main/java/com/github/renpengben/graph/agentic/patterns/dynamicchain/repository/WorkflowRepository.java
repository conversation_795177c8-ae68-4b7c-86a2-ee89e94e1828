
package com.github.renpengben.graph.agentic.patterns.dynamicchain.repository;

import com.github.renpengben.graph.agentic.patterns.dynamicchain.model.EdgeDefinition;
import com.github.renpengben.graph.agentic.patterns.dynamicchain.model.NodeDefinition;
import com.github.renpengben.graph.agentic.patterns.dynamicchain.model.WorkflowDefinition;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * 工作流仓库的模拟实现。
 * 在一个真实的应用程序中，这个类会与数据库（例如，使用Spring Data JPA）交互
 * 来获取工作流、节点和边的定义。在这个示例中，它返回硬编码的数据，
 * 用以复制原始的“chain”工作流。
 */
@Repository
public class WorkflowRepository {

    /**
     * 根据唯一名称查找工作流定义。
     * @param name 工作流的名称。
     * @return 一个 {@link WorkflowDefinition} 实例，如果未找到则返回null。
     */
    public WorkflowDefinition findWorkflowDefinitionByName(String name) {
        if ("chain_workflow".equals(name)) {
            // 定义工作流的名称及其在状态中将使用的所有键。
            return new WorkflowDefinition("chain_workflow",
                    List.of("inputText", "step1Text", "step2Text", "step3Text", "result", "checked"));
        }
        return null;
    }

    /**
     * 查找与给定工作流名称关联的所有节点定义。
     * @param workflowName 工作流的名称。
     * @return 一个 {@link NodeDefinition} 的列表。
     */
    public List<NodeDefinition> findNodeDefinitionsByWorkflowName(String workflowName) {
        if ("chain_workflow".equals(workflowName)) {
            // 定义图的5个节点：4个LLM步骤和1个代码门。
            // 每个LLM步骤的配置（prompt、输入/输出键）都作为JSON字符串存储。
            return List.of(
                    new NodeDefinition("step1", "LLM_STEP", """
                            {"systemPrompt":"Extract only the numerical values and their associated metrics from the text. Format each as 'value: metric' on a new line.","inputKey":"inputText","outputKey":"step1Text"}
                            """
                    ),
                    new NodeDefinition("gate", "CODE_GATE", "{}"),
                    new NodeDefinition("step2", "LLM_STEP", """
                            {"systemPrompt":"Convert all numerical values to percentages where possible. If not a percentage or points, convert to decimal (e.g., 92 points -> 92%).","inputKey":"step1Text","outputKey":"step2Text"}
                            """
                    ),
                    new NodeDefinition("step3", "LLM_STEP", """
                            {"systemPrompt":"Sort all lines in descending order by numerical value. Keep the format 'value: metric' on each line.","inputKey":"step2Text","outputKey":"step3Text"}
                            """
                    ),
                    new NodeDefinition("step4", "LLM_STEP", """
                            {"systemPrompt":"Format the sorted data as a markdown table with columns: | Metric | Value |","inputKey":"step3Text","outputKey":"result"}
                            """
                    )
            );
        }
        return List.of();
    }

    /**
     * 查找与给定工作流名称关联的所有边定义。
     * @param workflowName 工作流的名称。
     * @return 一个 {@link EdgeDefinition} 的列表。
     */
    public List<EdgeDefinition> findEdgeDefinitionsByWorkflowName(String workflowName) {
        if ("chain_workflow".equals(workflowName)) {
            // 定义节点之间的连接。
            // 包括一个从"gate"节点出发的条件边。
            return List.of(
                    new EdgeDefinition("START", "step1"),
                    new EdgeDefinition("step1", "gate"),
                    new EdgeDefinition("gate", "checked", Map.of("pass", "step2", "fail", "END")),
                    new EdgeDefinition("step2", "step3"),
                    new EdgeDefinition("step3", "step4"),
                    new EdgeDefinition("step4", "END")
            );
        }
        return List.of();
    }
}
