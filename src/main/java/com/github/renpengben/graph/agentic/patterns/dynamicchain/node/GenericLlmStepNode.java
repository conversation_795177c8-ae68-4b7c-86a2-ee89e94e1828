
package com.github.renpengben.graph.agentic.patterns.dynamicchain.node;

import com.alibaba.cloud.ai.graph.OverAllState;
import com.alibaba.cloud.ai.graph.action.NodeAction;
import com.github.renpengben.graph.agentic.patterns.dynamicchain.model.LlmStepConfig;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.model.ChatResponse;

import java.util.Map;

/**
 * 一个通用的、可配置的LLM（大语言模型）节点动作实现。
 * 该节点从工作流状态中读取输入，调用AI模型，然后将结果写回状态。
 */
public class GenericLlmStepNode implements NodeAction {

    private final ChatClient client;
    private final LlmStepConfig config;

    /**
     * 构造函数。
     * @param client 用于与AI模型交互的ChatClient实例。
     * @param config 包含此节点具体配置（如prompt, 输入/输出键）的对象。
     */
    public GenericLlmStepNode(ChatClient client, LlmStepConfig config) {
        this.client = client;
        this.config = config;
    }

    /**
     * 执行节点的核心逻辑。
     * @param state 整个工作流的共享状态。
     * @return 一个Map，其中包含需要更新到状态中的键值对。
     */
    @Override
    public Map<String, Object> apply(OverAllState state) {
        // 1. 根据配置的inputKey从状态中获取输入文本
        String text = (String) state.value(config.getInputKey()).orElse("");
        // 2. 使用配置的systemPrompt和输入文本，调用大语言模型
        ChatResponse resp = client.prompt()
                .system(config.getSystemPrompt())
                .user(text)
                .call()
                .chatResponse();
        String stepResult = resp.getResult().getOutput().getText();
        // 3. 将LLM的输出结果，以配置的outputKey为键，存入一个Map中返回
        return Map.of(config.getOutputKey(), stepResult);
    }
}
