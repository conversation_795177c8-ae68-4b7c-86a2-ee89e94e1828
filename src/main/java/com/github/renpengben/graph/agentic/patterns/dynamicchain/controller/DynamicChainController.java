
package com.github.renpengben.graph.agentic.patterns.dynamicchain.controller;

import com.alibaba.cloud.ai.graph.CompiledGraph;
import com.alibaba.cloud.ai.graph.StateGraph;
import com.alibaba.cloud.ai.graph.exception.GraphStateException;
import com.github.renpengben.graph.agentic.patterns.dynamicchain.service.DynamicGraphBuilder;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 用于动态工作流的REST控制器。
 * 它暴露一个API端点，允许客户端通过名称来调用和执行任何已定义的动态工作流。
 */
@RestController
@RequestMapping("/dynamic-chain")
public class DynamicChainController {

    private final DynamicGraphBuilder graphBuilder;

    public DynamicChainController(DynamicGraphBuilder graphBuilder) {
        this.graphBuilder = graphBuilder;
    }

    /**
     * 执行一个动态构建的工作流。
     * @param workflowName URL路径中的工作流名称，例如 "chain_workflow"。
     * @param text 请求体中传入的初始输入文本。
     * @return 工作流执行完毕后的最终状态数据。
     * @throws GraphStateException 如果图的编译或执行过程中发生错误。
     */
    @PostMapping("/{workflowName}/invoke")
    public Map<String, Object> invokeGraph(@PathVariable String workflowName, @RequestBody String text) throws GraphStateException {
        // 1. 使用DynamicGraphBuilder根据名称获取（或动态构建）StateGraph
        StateGraph graph = graphBuilder.buildGraphByName(workflowName);
        // 2. 编译图以获得可执行的版本
        CompiledGraph compiledGraph = graph.compile();
        // 3. 执行图，并传入初始输入，然后返回最终结果
        return compiledGraph.invoke(Map.of("inputText", text)).get().data();
    }
}
